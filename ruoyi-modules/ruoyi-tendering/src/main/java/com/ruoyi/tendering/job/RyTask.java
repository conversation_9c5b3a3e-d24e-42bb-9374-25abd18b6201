package com.ruoyi.tendering.job;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.tendering.domain.Article;
import com.ruoyi.tendering.domain.CrawlerContent;
import com.ruoyi.tendering.domain.CrawlerKeyword;
import com.ruoyi.tendering.service.ICrawlerContentService;
import com.ruoyi.tendering.service.ICrawlerKeywordService;
import com.ruoyi.tendering.utils.KeywordMatchUtil;
import com.ruoyi.tendering.utils.SimpleCrawlJob;
import com.ruoyi.tendering.utils.SimpleCrawlJobThree;
import com.ruoyi.tendering.utils.SimpleCrawlJobTwo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component
@EnableScheduling
public class RyTask {
    @Autowired
    private SimpleCrawlJob simpleCrawlJob;
    @Autowired
    private SimpleCrawlJobTwo simpleCrawlJobTwo;
    @Autowired
    private SimpleCrawlJobThree simpleCrawlJobThree;
    @Autowired
    private ICrawlerKeywordService crawlerKeywordService;
    @Autowired
    private ICrawlerContentService contentService;

    // 线程池 - 使用20个线程处理所有爬虫任务，提供更好的并发性能
    private final ExecutorService executorService = Executors.newFixedThreadPool(20);

//    @PostConstruct
    public void s() throws SQLException {
        List<CrawlerContent> crawlerContent1 = contentService.selectLastTime(7);

        List<CrawlerContent> crawlerContents1 = simpleCrawlJob.jinCaiNet(crawlerContent1);

        System.out.println(JSON.toJSON(crawlerContents1));

        getPost(crawlerContent1, crawlerContents1);

    }


    @Scheduled(cron = "0 0 1/5 * * ?")
    public void crawlerRenewalOne() {
        CrawlerTaskExecutor.executeTasks(executorService, "第一组爬虫任务",
            CrawlerTaskExecutor.task("中基国际", () ->
                intUrlContent(1, "https://www.cbbidding.com/Index/cms/id/", ".html", 20)),

            CrawlerTaskExecutor.task("国际招标", () ->
                intUrlContent(2, "http://www.nbbidding.com/Home/Notice/news_detail?id=", null, 10)),

            CrawlerTaskExecutor.task("宁波市政府采购网", () -> {
                List<CrawlerKeyword> crawlerKeywords = crawlerKeywordService.selectCrawlerKeywordList();
                List<CrawlerContent> crawlerContents = simpleCrawlJob.ningBoPurchase(crawlerKeywords);
                integration(3, crawlerContents);
            }),

            CrawlerTaskExecutor.task("中国邮政", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.chinaPost();
                integration(4, crawlerContents);
            }),

            CrawlerTaskExecutor.task("李惠利医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.lhlHospital();
                integration(5, crawlerContents);
            }),

            CrawlerTaskExecutor.task("鄞州人民医院", () ->
                intUrlContent(6, "https://www.nbyzyy.com/Page/News/Detail/", "?FID=41", 20)),

            CrawlerTaskExecutor.task("金采网", () -> {
                List<CrawlerContent> crawlerContent1 = contentService.selectLastTime(7);
                List<CrawlerContent> crawlerContents1 = simpleCrawlJob.jinCaiNet(crawlerContent1);
                getPost(crawlerContent1, crawlerContents1);
            }),

            CrawlerTaskExecutor.task("阳光采购", () -> {
                List<CrawlerContent> crawlerContent2 = contentService.selectLastTime(8);
                List<CrawlerContent> crawlerContents2 = simpleCrawlJob.ningBoSunshinePurchasing();
                getPost(crawlerContent2, crawlerContents2);
            }),

            CrawlerTaskExecutor.task("中医院", () -> {
                List<CrawlerContent> crawlerContents4 = simpleCrawlJob.ningBoChineseHospital();
                integration(10, crawlerContents4);
            }),

            CrawlerTaskExecutor.task("宁波国际投资咨询", () ->
                ningboInvestmentCrawler(43))
        );
    }

    @Scheduled(cron = "0 0 1/5 * * ?")
    public void crawlerRenewalTwo() {
        CrawlerTaskExecutor.executeTasks(executorService, "第二组爬虫任务",
            CrawlerTaskExecutor.task("康宁医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.healthyHospital();
                integration(11, crawlerContents);
            }),

            CrawlerTaskExecutor.task("眼科医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.ophthalmologyHospital();
                integration(12, crawlerContents);
            }),

            CrawlerTaskExecutor.task("第一附属医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.firstHospital();
                integration(13, crawlerContents);
            }),

            CrawlerTaskExecutor.task("第二附属医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.secondHospital();
                integration(14, crawlerContents);
            }),

            CrawlerTaskExecutor.task("妇女儿童医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.womenAndChildrenHospital();
                integration(15, crawlerContents);
            }),

            CrawlerTaskExecutor.task("附属人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.subordinatePeopleHospital();
                integration(16, crawlerContents);
            }),

            CrawlerTaskExecutor.task("第六医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.sixThHospital();
                integration(17, crawlerContents);
            }),

            CrawlerTaskExecutor.task("康复医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.rehabilitationHospital();
                integration(19, crawlerContents);
            }),

            CrawlerTaskExecutor.task("鄞州区第二医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJob.yzqSecondHospital();
                integration(20, crawlerContents);
            }),

            CrawlerTaskExecutor.task("精神病院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.psychosisHospital();
                integration(21, crawlerContents);
            })
        );
    }

    @Scheduled(cron = "0 0 1/5 * * ?")
    public void crawlerRenewalThree() {
        CrawlerTaskExecutor.executeTasks(executorService, "第三组爬虫任务",
            CrawlerTaskExecutor.task("北仑区人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.beiLunDistrictPeopleHospital();
                integration(22, crawlerContents);
            }),

            CrawlerTaskExecutor.task("余姚市人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.yuYaoPeopleHospital();
                integration(23, crawlerContents);
            }),

            CrawlerTaskExecutor.task("慈溪市人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.ciXiPeopleHospital();
                integration(24, crawlerContents);
            }),

            CrawlerTaskExecutor.task("杭州湾医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.hangZhouBayHospital();
                integration(25, crawlerContents);
            }),

            CrawlerTaskExecutor.task("宁海县第一医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.ningHaiSecondHospital();
                integration(26, crawlerContents);
            }),

            CrawlerTaskExecutor.task("镇海区人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.zhenHaiPeopleHospital();
                integration(27, crawlerContents);
            }),

            CrawlerTaskExecutor.task("镇海龙赛医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.zhenHaiDragonRaceHospital();
                integration(28, crawlerContents);
            }),

            CrawlerTaskExecutor.task("第九医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.ningBoNinthHospital();
                integration(29, crawlerContents);
            }),

            CrawlerTaskExecutor.task("第四人民医院", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.haiShuThirdHospital();
                integration(30, crawlerContents);
            }),

            CrawlerTaskExecutor.task("丽水市公共资源交易中心", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.liShuiTradingCenter();
                integration(31, crawlerContents);
            })
        );
    }

    @Scheduled(cron = "0 0 1/5 * * ?")
    public void crawlerRenewalFour() {
        CrawlerTaskExecutor.executeTasks(executorService, "第四组爬虫任务",
            CrawlerTaskExecutor.task("中冠工程咨询", () ->
                intUrlContent(32, "http://zb.zhongguanzixun.com/index.php?ctl=tender&act=", null, 20)),

            CrawlerTaskExecutor.task("中招联合招标", () ->
                intUrlContent(33, "http://www.365trade.com.cn/zhwzb/", ".jhtml", 20)),

            CrawlerTaskExecutor.task("中化商务电子招投标", () ->
                intUrlContent(34, "https://e.sinochemitc.com/cms/channel/ywgg1fw/", ".htm", 10)),

            CrawlerTaskExecutor.task("中国航空油料集团", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.chinaAviationFuel();
                integration(35, crawlerContents);
            }),

            CrawlerTaskExecutor.task("交通银行智采平台", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.transportationProvider();
                integration(37, crawlerContents);
            }),

            CrawlerTaskExecutor.task("诚E招电子采购", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.chengECorpuscle();
                integration(38, crawlerContents);
            }),

            CrawlerTaskExecutor.task("鄞州区基层小型平台", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.yinZhouSmall();
                integration(39, crawlerContents);
            }),

            CrawlerTaskExecutor.task("浙江海港电子招标", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobTwo.zheJiangElectronicBidding();
                integration(40, crawlerContents);
            }),

            CrawlerTaskExecutor.task("国家电网电子商务", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobThree.stateGridElectronicCommercePages(1, 10);
                integration(41, crawlerContents);
            }),

            CrawlerTaskExecutor.task("国铁采购平台", () -> {
                List<CrawlerContent> crawlerContents = simpleCrawlJobThree.railwayPurchasingOriginal();
                integration(42, crawlerContents);
            })
        );
    }

    // ==================== 以下是原有的辅助方法，保持不变 ====================

    //爬标题专用 - 优化版：批量处理减少数据库操作
    public void integration(Integer relevanceId, List<CrawlerContent> cc) {
        if (cc == null || cc.isEmpty()) {
            System.out.println("relevanceId=" + relevanceId + " 没有数据需要处理");
            return;
        }

        System.out.println("开始处理 relevanceId=" + relevanceId + " 的数据，共" + cc.size() + "条");

        // 1. 获取关键字（一次查询）
        List<CrawlerKeyword> crawlerKeywords = crawlerKeywordService.selectCrawlerKeywordList();

        // 2. 先在内存中过滤关键词匹配的数据
        List<CrawlerContent> matchedContents = cc.stream()
            .filter(Objects::nonNull) // 过滤空对象
            .filter(content -> {
                String title = content.getTitle();
                String contentText = content.getContent();

                // 如果标题为空，直接跳过
                if (title == null || title.trim().isEmpty()) {
                    return false;
                }

                // 使用工具类进行关键词匹配（工具类内部已处理内容为空的情况）
                boolean matched = KeywordMatchUtil.matchKeywords(title, contentText, crawlerKeywords);

                if (contentText == null || contentText.trim().isEmpty()) {
                    System.out.println("内容为空，只判断标题关键词: " + title + " -> " + (matched ? "匹配" : "不匹配"));
                } else {
                    System.out.println("标题和内容关键词匹配: " + title + " -> " + (matched ? "匹配" : "不匹配"));
                }

                return matched;
            })
            .collect(Collectors.toList());

        if (matchedContents.isEmpty()) {
            System.out.println("relevanceId=" + relevanceId + " 没有匹配关键词的数据");
            return;
        }

        System.out.println("relevanceId=" + relevanceId + " 关键词匹配后剩余" + matchedContents.size() + "条数据");

        // 3. 对爬取到的数据按URL去重（内存去重）
        List<CrawlerContent> memoryDeduplicatedContents = new ArrayList<>(matchedContents.stream()
            .filter(content -> content.getArticleAddress() != null && !content.getArticleAddress().trim().isEmpty())
            .collect(Collectors.toMap(
                CrawlerContent::getArticleAddress,
                content -> content,
                (existing, replacement) -> existing // 如果URL重复，保留第一个
            ))
            .values());

        System.out.println("relevanceId=" + relevanceId + " 内存去重前: " + matchedContents.size() + " 条，去重后: " + memoryDeduplicatedContents.size() + " 条");

        // 4. 数据库URL去重：批量检查URL是否已存在于数据库中
        List<String> urlsToCheck = memoryDeduplicatedContents.stream()
            .map(CrawlerContent::getArticleAddress)
            .filter(Objects::nonNull)
            .map(String::trim)
            .filter(url -> !url.isEmpty())
            .distinct()
            .collect(Collectors.toList());

        // 批量查询已存在的URL
        Set<String> existingUrls = contentService.checkUrlsExistBatch(urlsToCheck);

        // 过滤出不存在的数据
        List<CrawlerContent> newContents = memoryDeduplicatedContents.stream()
            .filter(content -> {
                String url = content.getArticleAddress();
                boolean exists = existingUrls.contains(url);
                if (exists) {
                    System.out.println("数据库中已存在URL，跳过: " + content.getTitle());
                }
                return !exists;
            })
            .collect(Collectors.toList());

        int duplicateInDbCount = memoryDeduplicatedContents.size() - newContents.size();
        System.out.println("relevanceId=" + relevanceId + " 数据库去重统计: 总数" + memoryDeduplicatedContents.size() +
                          ", 数据库重复" + duplicateInDbCount + ", 新增" + newContents.size() + "条");

        if (newContents.isEmpty()) {
            System.out.println("relevanceId=" + relevanceId + " 没有新数据需要插入");
            return;
        }

        System.out.println("relevanceId=" + relevanceId + " 准备批量插入" + newContents.size() + "条新数据");

        // 6. 批量插入新数据
        int insertCount = contentService.batchInsertCrawlerContent(newContents);
        System.out.println("relevanceId=" + relevanceId + " 批量插入完成，成功插入" + insertCount + "条数据");
    }

    //爬内容链接带数字更新 - 优化版：批量处理减少数据库操作
    public void intUrlContent(int relevanceId, String url1, String url2, int flag) throws InterruptedException {
        System.out.println("开始批量爬取 relevanceId=" + relevanceId + " 的数据");

        // 1. 获取关键字（一次查询）
        List<CrawlerKeyword> crawlerKeywords = crawlerKeywordService.selectCrawlerKeywordList();
        CrawlerContent lastCT = contentService.selectLastContent(relevanceId);
        Integer startArticleNumber = lastCT.getArticleNumber();

        // 2. 批量爬取数据
        List<CrawlerContent> validContents = new ArrayList<>();
        List<Integer> processedNumbers = new ArrayList<>();

        int consecutiveEmptyCount = 0;
        int totalEmptyCount = 0;
        int successCount = 0;
        final int MAX_CONSECUTIVE_EMPTY = flag;
        final int MAX_TOTAL_ATTEMPTS = flag * 5;
        final int MIN_SUCCESS_THRESHOLD = 2;

        int attemptCount = 0;
        Integer currentArticleNumber = startArticleNumber;
        boolean shouldContinue = true;

        while (shouldContinue && attemptCount < MAX_TOTAL_ATTEMPTS) {
            attemptCount++;
            currentArticleNumber++;

            System.out.println("第" + attemptCount + "次尝试，当前文章编号: " + currentArticleNumber);

            String idAdd = String.valueOf(currentArticleNumber);
            String url = url2 != null ? url1 + idAdd + url2 : url1 + idAdd;

            // 3. 根据不同来源获取文章内容
            Article article = fetchArticleByRelevanceId(relevanceId, url);

            if (isValidArticle(article)) {
                consecutiveEmptyCount = 0;
                successCount++;

                String title = article.getTitle();
                String content = article.getContent();

                // 4. 关键词匹配检查
                boolean shouldAdd = KeywordMatchUtil.matchKeywords(title, content, crawlerKeywords);
                String matchResult = "";

                if (content == null || content.trim().isEmpty()) {
                    matchResult = shouldAdd ? "内容为空，标题关键词匹配" : "内容为空，标题关键词不匹配";
                } else {
                    matchResult = shouldAdd ? "标题或内容关键词匹配" : "标题和内容关键词都不匹配";
                }

                if (shouldAdd) {
                    // 添加到待插入列表
                    CrawlerContent crawlerContent = new CrawlerContent();
                    crawlerContent.setRelevanceId(relevanceId);
                    crawlerContent.setArticleNumber(currentArticleNumber);
                    crawlerContent.setTitle(title);
                    crawlerContent.setReleaseTime(article.getTime());
                    crawlerContent.setContent(content);
                    crawlerContent.setArticleAddress(url);

                    validContents.add(crawlerContent);
                    System.out.println("✅ " + matchResult + "，文章编号: " + currentArticleNumber + ", 标题: " + title);
                } else {
                    System.out.println("⚠️ " + matchResult + "，文章编号: " + currentArticleNumber);
                }

                // 记录已处理的编号
                processedNumbers.add(currentArticleNumber);
            } else {
                consecutiveEmptyCount++;
                totalEmptyCount++;
                processedNumbers.add(currentArticleNumber);
                System.out.println("❌ 未获取到内容，连续空结果次数: " + consecutiveEmptyCount);
            }

            // 5. 停止条件检查
            if (consecutiveEmptyCount >= MAX_CONSECUTIVE_EMPTY) {
                if (successCount >= MIN_SUCCESS_THRESHOLD) {
                    if (consecutiveEmptyCount >= MAX_CONSECUTIVE_EMPTY * 2) {
                        shouldContinue = false;
                        System.out.println("连续空结果次数过多，停止爬取");
                    }
                } else {
                    shouldContinue = false;
                    System.out.println("成功获取内容次数不足，停止爬取");
                }
            }

            Thread.sleep(100); // 避免请求过于频繁
        }

        // 6. 批量插入有效数据
        if (!validContents.isEmpty()) {
            System.out.println("准备批量插入 " + validContents.size() + " 条有效数据");
            int insertCount = contentService.batchInsertCrawlerContent(validContents);
            System.out.println("批量插入完成，成功插入 " + insertCount + " 条数据");
        }

        // 7. 更新最大文章编号（只有在有有效数据时才更新）
        if (!validContents.isEmpty() && !processedNumbers.isEmpty()) {
            // 只更新到最后一个有效数据的编号
            Integer maxValidNumber = validContents.stream()
                .mapToInt(content -> content.getArticleNumber())
                .max()
                .orElse(startArticleNumber);

            CrawlerContent updateContent = new CrawlerContent();
            updateContent.setId(lastCT.getId());
            updateContent.setArticleNumber(maxValidNumber);
            contentService.updatecrawlerContent(updateContent);
            System.out.println("更新最大文章编号为: " + maxValidNumber + " (只更新到最后有效数据)");
        } else {
            System.out.println("⚠️ 没有有效数据，不更新最大文章编号，保持为: " + startArticleNumber);
        }

        System.out.println("🎉 批量爬取完成 - 总尝试次数: " + attemptCount +
                         ", 成功次数: " + successCount +
                         ", 有效数据: " + validContents.size() +
                         ", 总空结果次数: " + totalEmptyCount);
    }

    /**
     * 修复错误的最大文章编号
     * 将数据库中的最大ID重置为网站实际存在的最大ID
     *
     * @param relevanceId 关联ID
     * @param actualMaxId 网站实际存在的最大ID
     */
    public void fixMaxArticleNumber(int relevanceId, int actualMaxId) {
        try {
            System.out.println("🔧 开始修复 relevanceId=" + relevanceId + " 的最大文章编号");

            // 获取当前数据库中的最大ID记录
            CrawlerContent lastCT = contentService.selectLastContent(relevanceId);
            if (lastCT == null) {
                System.err.println("❌ 未找到 relevanceId=" + relevanceId + " 的记录");
                return;
            }

            int currentMaxId = lastCT.getArticleNumber();
            System.out.println("📊 当前数据库最大ID: " + currentMaxId);
            System.out.println("📊 网站实际最大ID: " + actualMaxId);

            if (currentMaxId <= actualMaxId) {
                System.out.println("✅ 数据库ID正常，无需修复");
                return;
            }

            // 更新为正确的最大ID
            CrawlerContent updateContent = new CrawlerContent();
            updateContent.setId(lastCT.getId());
            updateContent.setArticleNumber(actualMaxId);

            int result = contentService.updatecrawlerContent(updateContent);
            if (result > 0) {
                System.out.println("✅ 修复成功！最大文章编号已从 " + currentMaxId + " 重置为 " + actualMaxId);
            } else {
                System.err.println("❌ 修复失败，数据库更新失败");
            }

        } catch (Exception e) {
            System.err.println("❌ 修复最大文章编号异常: " + e.getMessage());
        }
    }

    /**
     * 根据 relevanceId 获取文章内容
     */
    private Article fetchArticleByRelevanceId(int relevanceId, String url) {
        try {
            Article article;
            switch (relevanceId) {
                case 1:
                    article = simpleCrawlJob.chinaFoundationInternational(url);
                    break;
                case 2:
                    article = simpleCrawlJob.InternationalBidding(url);
                    break;
                case 6:
                    article = simpleCrawlJob.ningBoPeoplesHospital(url);
                    break;
                case 32:
                    article = simpleCrawlJobTwo.zhongGuanProject(url);
                    break;
                case 33:
                    article = simpleCrawlJobTwo.chinaJointProcurement(url);
                    break;
                case 34:
                    article = simpleCrawlJobTwo.zhongHuaBusiness(url);
                    break;
                default:
                    return new Article(); // 默认空对象
            }

            // 对获取到的文章进行时间格式化和验证
            return processArticleTime(article);

        } catch (Exception e) {
            System.err.println("获取文章内容异常，URL: " + url + ", 错误: " + e.getMessage());
            return new Article();
        }
    }

    /**
     * 处理文章时间格式化和验证
     */
    private Article processArticleTime(Article article) {
        if (article == null) {
            return new Article();
        }

        String originalTime = article.getTime();

        String formattedTime = DateUtils.formatAndValidateTime(originalTime);

        // 如果时间格式化失败或为空，返回空文章
        if (formattedTime == null || formattedTime.trim().isEmpty()) {
            System.out.println("文章时间无效，过滤掉该文章。原始时间: " + originalTime);
            return new Article();
        }

        // 设置格式化后的时间
        article.setTime(formattedTime);
        return article;
    }



    /**
     * 检查文章是否有效（标题不能为空，时间不能为空）
     */
    private boolean isValidArticle(Article article) {
        return article != null &&
               article.getTitle() != null &&
               !article.getTitle().trim().isEmpty() &&
               article.getTime() != null &&
               !article.getTime().trim().isEmpty();
    }

    //传参调接口爬取 - 优化版：批量处理减少数据库操作
    public void getPost(List<CrawlerContent> c1, List<CrawlerContent> cs) throws SQLException {
        if (cs == null || cs.isEmpty()) {
            System.out.println("没有数据需要处理");
            return;
        }

        System.out.println("开始处理数据，共" + cs.size() + "条");

        // 1. 获取关键字（一次查询）
        List<CrawlerKeyword> crawlerKeywords = crawlerKeywordService.selectCrawlerKeywordList();

        // 2. 对当前爬取的数据按URL去重
        List<CrawlerContent> deduplicatedContents = new ArrayList<>(cs.stream()
            .filter(Objects::nonNull)
            .filter(content -> content.getArticleAddress() != null && !content.getArticleAddress().trim().isEmpty())
            .collect(Collectors.toMap(
                CrawlerContent::getArticleAddress,
                content -> content,
                (existing, replacement) -> existing // 如果URL重复，保留第一个
            ))
            .values());


        System.out.println("去重前: " + cs.size() + " 条，去重后: " + deduplicatedContents.size() + " 条");

        // 3. 确定时间阈值
        String releaseTime;
        if (c1.isEmpty()) {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            calendar.add(Calendar.DATE, -3);
            releaseTime = dateFormat.format(calendar.getTime());
        } else {
            releaseTime = c1.get(0).getReleaseTime();
        }

        // 3. 批量过滤数据：时间过滤 + 关键词匹配
        List<CrawlerContent> validContents = deduplicatedContents.stream()
            .filter(content -> content.getReleaseTime() != null && content.getReleaseTime().compareTo(releaseTime) >= 0)
            .filter(content -> {
                String title = content.getTitle();
                String contentText = content.getContent();

                // 如果标题为空，直接跳过
                if (title == null || title.trim().isEmpty()) {
                    return false;
                }

                // 使用工具类进行关键词匹配（工具类内部已处理内容为空的情况）
                boolean matched = KeywordMatchUtil.matchKeywords(title, contentText, crawlerKeywords);

                if (contentText == null || contentText.trim().isEmpty()) {
                    System.out.println("内容为空，只判断标题关键词: " + title + " -> " + (matched ? "匹配" : "不匹配"));
                } else {
                    System.out.println("标题和内容关键词匹配: " + title + " -> " + (matched ? "匹配" : "不匹配"));
                }

                return matched;
            })
            .collect(Collectors.toList());

        if (validContents.isEmpty()) {
            System.out.println("没有符合条件的新数据需要插入");
            return;
        }

        System.out.println("关键词匹配后剩余" + validContents.size() + "条数据");

        // 4. 数据库URL去重：批量检查URL是否已存在于数据库中
        List<String> urlsToCheck = validContents.stream()
            .map(CrawlerContent::getArticleAddress)
            .filter(Objects::nonNull)
            .map(String::trim)
            .filter(url -> !url.isEmpty())
            .distinct()
            .collect(Collectors.toList());

        // 批量查询已存在的URL
        Set<String> existingUrls = contentService.checkUrlsExistBatch(urlsToCheck);

        // 过滤出不存在的数据
        List<CrawlerContent> newContents = validContents.stream()
            .filter(content -> {
                String url = content.getArticleAddress();
                boolean exists = existingUrls.contains(url);
                if (exists) {
                    System.out.println("数据库中已存在URL，跳过: " + content.getTitle());
                }
                return !exists;
            })
            .collect(Collectors.toList());

        int duplicateInDbCount = validContents.size() - newContents.size();
        System.out.println("数据库去重统计: 总数" + validContents.size() +
                          ", 数据库重复" + duplicateInDbCount + ", 新增" + newContents.size() + "条");

        if (newContents.isEmpty()) {
            System.out.println("所有数据在数据库中都已存在，无需插入");
            return;
        }

        System.out.println("最终需要插入" + newContents.size() + "条数据");

        // 5. 按 relevanceId 分组处理（因为有特殊的 relevanceId=9 需要特殊处理）
        Map<Integer, List<CrawlerContent>> groupedByRelevanceId = newContents.stream()
            .collect(Collectors.groupingBy(CrawlerContent::getRelevanceId));

        int totalInserted = 0;

        for (Map.Entry<Integer, List<CrawlerContent>> entry : groupedByRelevanceId.entrySet()) {
            Integer relevanceId = entry.getKey();
            List<CrawlerContent> contents = entry.getValue();

            if (relevanceId == 9) {
                // 特殊处理 relevanceId=9 的数据（使用直连数据库）
                totalInserted += batchInsertWithDirectConnection(contents);
            } else {
                // 普通数据使用 Service 批量插入
                totalInserted += contentService.batchInsertCrawlerContent(contents);
            }
        }

        System.out.println("批量插入完成，成功插入" + totalInserted + "条数据");
    }

    /**
     * 使用直连数据库批量插入（针对 relevanceId=9 的特殊情况）
     */
    private int batchInsertWithDirectConnection(List<CrawlerContent> contents) throws SQLException {
        if (contents.isEmpty()) {
            return 0;
        }

        String url = "jdbc:mysql://*************/crawler?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8";
        String sql = "INSERT INTO crawler_content (relevance_id,title,release_time,content,article_address) VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE release_time = VALUES(release_time)";

        try (Connection connection = DriverManager.getConnection(url, "root", "Ninan987123!?");
             PreparedStatement stmt = connection.prepareStatement(sql)) {

            connection.setAutoCommit(false); // 开启事务

            try {
                for (CrawlerContent content : contents) {
                    stmt.setInt(1, content.getRelevanceId());
                    stmt.setString(2, content.getTitle());
                    stmt.setString(3, content.getReleaseTime());
                    stmt.setString(4, content.getContent());
                    stmt.setString(5, content.getArticleAddress());
                    stmt.addBatch();
                }

                int[] results = stmt.executeBatch();
                connection.commit(); // 提交事务

                return results.length;
            } catch (SQLException e) {
                connection.rollback(); // 回滚事务
                System.err.println("批量插入失败，事务已回滚: " + e.getMessage());
                throw e;
            }
        }
    }

    /**
     * 宁波国际投资咨询有限公司独立爬虫方法
     * @param relevanceId 关联ID (43)
     */
    public void ningboInvestmentCrawler(int relevanceId) throws InterruptedException {
        System.out.println("开始执行宁波国际投资咨询有限公司爬虫任务");

        //关键字
        List<CrawlerKeyword> crawlerKeywords = crawlerKeywordService.selectCrawlerKeywordList();
        CrawlerContent lastCT = contentService.selectLastContent(relevanceId);
        Integer articleNumber = lastCT.getArticleNumber();

        // 改进的重试逻辑
        int consecutiveEmptyCount = 0; // 连续空结果计数
        int totalEmptyCount = 0; // 总空结果计数
        int successCount = 0; // 成功获取内容计数
        final int MAX_CONSECUTIVE_EMPTY = 20; // 最大连续空结果次数
        final int MAX_TOTAL_ATTEMPTS = 50; // 最大总尝试次数，防止无限循环
        final int MIN_SUCCESS_THRESHOLD = 2; // 最少成功获取内容次数，确保有数据

        int attemptCount = 0;
        boolean shouldContinue = true;

        while (shouldContinue && attemptCount < MAX_TOTAL_ATTEMPTS) {
            attemptCount++;
            System.out.println("第" + attemptCount + "次尝试，当前文章编号: " + (articleNumber + 1));

            String idAdd = String.valueOf(articleNumber + 1);
            String url = "http://www.nbgodo.com/info.aspx?id=" + idAdd + "&chid=11";

            // 获取文章内容
            Article article = null;
            try {
                article = simpleCrawlJob.ningboInternationalInvestment(url);
            } catch (Exception e) {
                System.err.println("爬取URL异常: " + url + ", 错误: " + e.getMessage());
                article = new Article();
            }

            boolean hasNewContent = article != null &&
                                  article.getTitle() != null &&
                                  !article.getTitle().trim().isEmpty() &&
                                  article.getContent() != null &&
                                  !article.getContent().trim().isEmpty();

            if (hasNewContent) {
                consecutiveEmptyCount = 0; // 重置连续空计数器
                successCount++;

                String title = article.getTitle();
                String content = article.getContent();

                if (!KeywordMatchUtil.matchKeywords(title, content, crawlerKeywords)) {
                    System.out.println("内容不包含关键词，跳过: " + title);
                    articleNumber += 1;
                    CrawlerContent crawlerContent = new CrawlerContent();
                    crawlerContent.setId(lastCT.getId());
                    crawlerContent.setArticleNumber(articleNumber);
                    contentService.updatecrawlerContent(crawlerContent);
                    continue;
                }

                CrawlerContent crawlerContent = new CrawlerContent();
                crawlerContent.setRelevanceId(relevanceId);
                crawlerContent.setArticleNumber(articleNumber + 1);
                crawlerContent.setTitle(title);
                crawlerContent.setReleaseTime(article.getTime());
                crawlerContent.setContent(content);
                crawlerContent.setArticleAddress(url);

                System.out.println("成功获取内容: " + title);
                System.out.println("发布时间: " + article.getTime());
                System.out.println("内容长度: " + content.length());

                if (contentService.insertcrawlerContent(crawlerContent) > 0) {
                    articleNumber += 1;
                    System.out.println("数据保存成功，当前文章编号: " + articleNumber);
                }
            } else {
                articleNumber += 1;
                consecutiveEmptyCount++;
                totalEmptyCount++;
                System.out.println("未获取到内容，连续空结果次数: " + consecutiveEmptyCount + ", URL: " + url);
            }

            // 更智能的停止条件
            if (consecutiveEmptyCount >= MAX_CONSECUTIVE_EMPTY) {
                if (successCount >= MIN_SUCCESS_THRESHOLD) {
                    // 如果之前有成功获取内容，给更多机会
                    if (consecutiveEmptyCount >= MAX_CONSECUTIVE_EMPTY * 2) {
                        shouldContinue = false;
                        System.out.println("连续空结果次数过多，停止爬取");
                    }
                } else {
                    // 如果成功次数太少，提前停止
                    shouldContinue = false;
                    System.out.println("成功获取内容次数不足，停止爬取");
                }
            }

            // 添加短暂延迟，避免请求过于频繁
            Thread.sleep(100);
        }

        System.out.println("宁波国际投资咨询爬虫任务完成 - 总尝试次数: " + attemptCount +
                         ", 成功次数: " + successCount + ", 总空结果次数: " + totalEmptyCount);
    }



    /**
     * 应用关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        System.out.println("正在关闭爬虫任务线程池...");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
                System.out.println("线程池强制关闭");
            } else {
                System.out.println("线程池关闭完成");
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
