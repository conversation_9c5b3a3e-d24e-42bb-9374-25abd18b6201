package com.ruoyi.tendering.job;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 爬虫任务执行器工具类
 * 用于批量执行爬虫任务，自动处理线程池提交和异常处理
 *
 * <AUTHOR>
 */
public class CrawlerTaskExecutor {

    /**
     * 任务定义类
     */
    public static class CrawlerTask {
        private final String name;
        private final Runnable task;

        public CrawlerTask(String name, Runnable task) {
            this.name = name;
            this.task = task;
        }

        public String getName() { return name; }
        public Runnable getTask() { return task; }
    }

    /**
     * 批量执行爬虫任务
     *
     * @param executorService 线程池
     * @param groupName 任务组名称
     * @param tasks 任务列表
     */
    public static void executeTasks(ExecutorService executorService, String groupName, CrawlerTask... tasks) {
        System.out.println("=== 开始执行" + groupName + " ===");

        List<Future<?>> futures = new ArrayList<>();

        // 提交所有任务到线程池
        for (CrawlerTask crawlerTask : tasks) {
            futures.add(executorService.submit(() -> {
                try {
                    crawlerTask.getTask().run();
                    System.out.println("✅ " + crawlerTask.getName() + "更新成功");
                } catch (Exception e) {
                    System.err.println("❌ " + crawlerTask.getName() + "更新失败: " + e.getMessage());
                }
            }));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                System.err.println("任务执行异常: " + e.getMessage());
            }
        }

        System.out.println("🎉 " + groupName + "全部完成");
    }

    /**
     * 创建任务的便捷方法
     */
    public static CrawlerTask task(String name, Runnable task) {
        return new CrawlerTask(name, task);
    }
}
