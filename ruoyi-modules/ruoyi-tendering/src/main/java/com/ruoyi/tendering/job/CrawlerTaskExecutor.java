package com.ruoyi.tendering.job;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 爬虫任务执行器工具类
 * 用于封装线程池任务提交和异常处理逻辑
 * 
 * <AUTHOR>
 */
public class CrawlerTaskExecutor {
    
    private final ExecutorService executorService;
    private final List<Future<?>> futures;
    
    public CrawlerTaskExecutor(ExecutorService executorService) {
        this.executorService = executorService;
        this.futures = new ArrayList<>();
    }
    
    /**
     * 提交任务到线程池
     * 
     * @param taskName 任务名称，用于日志输出
     * @param task 要执行的任务
     */
    public void submitTask(String taskName, Runnable task) {
        futures.add(executorService.submit(() -> {
            try {
                task.run();
                System.out.println("✅ " + taskName + "更新成功");
            } catch (Exception e) {
                System.err.println("❌ " + taskName + "更新失败: " + e.getMessage());
            }
        }));
    }
    
    /**
     * 等待所有任务完成
     */
    public void waitForAllTasks() {
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                System.err.println("任务执行异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取已提交的任务数量
     */
    public int getTaskCount() {
        return futures.size();
    }
}
